<!DOCTYPE html>
<html>
<head>
    <title>统一回调反馈测试</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>统一回调反馈机制测试</h1>
    
    <div id="results"></div>
    
    <script>
    async function testCallback(data, testName, expectedResponse) {
        const resultsDiv = document.getElementById('results');
        
        try {
            const response = await fetch('/api/transaction/unifiedCallback', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams(data)
            });
            
            const result = await response.text();
            const isSuccess = result === expectedResponse;
            
            resultsDiv.innerHTML += `
                <div style="margin: 10px 0; padding: 10px; border: 1px solid ${isSuccess ? 'green' : 'red'};">
                    <h3>${testName}</h3>
                    <p><strong>期望结果:</strong> ${expectedResponse}</p>
                    <p><strong>实际结果:</strong> ${result}</p>
                    <p><strong>测试状态:</strong> ${isSuccess ? '✅ 通过' : '❌ 失败'}</p>
                    <p><strong>HTTP状态:</strong> ${response.status}</p>
                </div>
            `;
        } catch (error) {
            resultsDiv.innerHTML += `
                <div style="margin: 10px 0; padding: 10px; border: 1px solid red;">
                    <h3>${testName}</h3>
                    <p><strong>错误:</strong> ${error.message}</p>
                    <p><strong>测试状态:</strong> ❌ 失败</p>
                </div>
            `;
        }
    }
    
    // 开始测试
    window.onload = async function() {
        const resultsDiv = document.getElementById('results');
        resultsDiv.innerHTML = '<h2>正在执行测试...</h2>';
        
        // 测试1: WatchPay充值回调
        await testCallback({
            'tradeResult': '1',
            'mchOrderNo': 'SY2103230112372048',
            'mchId': '977977802',
            'amount': '500.00',
            'orderNo': '600993278',
            'sign': '3a02aec2e60818687daa7837d33464f2',
            'signType': 'MD5'
        }, 'WatchPay充值回调', 'success');
        
        // 测试2: JayaPay充值回调
        await testCallback({
            'code': '00',
            'msg': 'SUCCESS',
            'status': 'SUCCESS',
            'platOrderNum': 'BK_1563278763273',
            'orderNum': 'T1231511321515',
            'payMoney': '100000',
            'platSign': 'ja6R8eukQY9jc8zrhtf34654ungj7u8sdgdfjfs'
        }, 'JayaPay充值回调', 'SUCCESS');
        
        // 测试3: 修复前的错误参数（应该识别为unknown）
        await testCallback({
            'orderid': '123456',
            'status': 'success',
            'trade_status': 'completed'
        }, '修复前的错误参数测试', 'success');
        
        // 测试4: 未知类型回调
        await testCallback({
            'unknown_param': 'test',
            'other_param': 'value'
        }, '未知类型回调', 'success');
        
        resultsDiv.innerHTML = '<h2>测试完成！</h2>' + resultsDiv.innerHTML.replace('<h2>正在执行测试...</h2>', '');
    };
    </script>
</body>
</html>
